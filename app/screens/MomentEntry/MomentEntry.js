import React from 'react';
import {
  StyleSheet,
  Dimensions,
  Platform,
  Alert,
  InteractionManager,
  Keyboard,
  View,
  Text,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import Meteor from 'react-native-meteor';
import {Container, ActionSheet, Button} from 'native-base';
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view';
import moment from 'moment';
import ImagePicker from 'react-native-image-crop-picker';
import SyncManager from '../../shared/SyncManager';
import MomentDefinitions from '../../shared/MomentDefinitions';
import _ from 'lodash';
import Orgs from '../../api/Orgs';
import Curriculums from '../../api/Curriculums';
import Food from '../../api/Food';
import NativePicker from 'react-native-picker';
import LoadingScreen from '../../components/LoadingScreen';
import OptionalSafeAreaView from '../../components/OptionalSafeAreaView';
import {getPersonIdForMomentEntry} from '../../helpers/NavState';
import colors from '../../config/colors.json';
import {CommonActions} from '@react-navigation/native';
import Bugsnag from '@bugsnag/react-native';
import Dialog from 'react-native-dialog';
import {SegmentedMessage} from 'sms-segments-calculator';
import {imagePicker} from '../../helpers/ImagePicker';

const GSM_7 = 153; // multi part body length
const UCS_2 = 67; // multi part body lenght

import {
  MomentPicker,
  Header,
  Tag,
  DateField,
  TimeField,
  CommentField,
  MediaField,
  ButtonsField,
  CheckBoxField,
  AmountField,
  SelectField,
  SelectMultiple,
  StringField,
  TextField,
  TimePickerField,
  AssessmentsField,
  MomentAttribution,
  PayTypeField,
} from './components';
import {preSave} from '../../utility/moments';
import People from '../../api/People';
import Groups from '../../api/Groups';

const parseSegmentedMessage = str => {
  const segmentedMessage = new SegmentedMessage(str);
  const subStrLength = segmentedMessage.encodingName == 'GSM-7' ? GSM_7 : UCS_2;
  return {str: str.substr(0, subStrLength), smsEncodingLength: subStrLength};
};
const noCheckInMoments = ['alert', 'portfolio', 'learning']
const height = Dimensions.get('window').height;
const styles = StyleSheet.create({
  dialogInput: {
    ...Platform.select({
      ios: {
        height: height * 0.2,
      },
      android: {
        height: height * 0.2,
        borderBottomColor: colors.black,
        borderBottomWidth: 2,
        color: colors.black,
      },
    }),
  },
});

class MomentEntry extends React.Component {
  constructor(props) {
    super(props);

    const passedMomentType = props.route?.params?.newMomentType;
    const passedTaggedPeople = props.route?.params?.taggedPeople;
    const passedEditMoment = props.route?.params?.editMoment;
    const passedAttachedMedia = props.route?.params?.attachedMedia;
    const familyCheckIn = props.route?.params?.familyCheckIn;
    const tabIndex = props.route?.params?.tabIndex ?? null;
    const hideAddMediaButton = props.route?.params?.hideAddMediaButton ?? false;
    
    const activeMomentType = passedEditMoment
      ? passedEditMoment.momentType
      : passedMomentType == 'other'
      ? 'comment'
      : passedMomentType;
    let mappedDefaultValues = {};

    if (passedEditMoment && passedEditMoment.dynamicFieldValues) {
      _.each(passedEditMoment.dynamicFieldValues, (v, k) => {
        passedEditMoment[k] = v;
      });
    }

    if (passedMomentType) {
      const momentDefinitions = new MomentDefinitions(passedMomentType, {
          org: this.props.currentOrg,
        }),
        momentDefinition = momentDefinitions.getDefinition();
      if (passedMomentType == 'informedArrival' && familyCheckIn) {
        mappedDefaultValues = familyCheckIn.formFieldData
          ? familyCheckIn.formFieldData
          : {};
      }
      if (momentDefinition)
        _.chain(momentDefinition.fields)
          .filter(f => f.defaultFieldValue)
          .each(f => {
            mappedDefaultValues[f.dataId] = f.defaultFieldValue;
          });
    }
    const attributionPersonId = _.get(
      passedEditMoment,
      'attributionPersonId',
      null,
    );
    const attributionPerson = attributionPersonId
      ? {
          _id: attributionPersonId,
          firstName: 'Unavailable',
          lastName: 'To View',
        }
      : {};

    this.state = {
      tabIndex,
      hideAddMediaButton,
      availableStaffAdmins: [],
      attributionPerson,
      showMomentPicker: passedMomentType === 'other',
      showTimePicker: false,
      showDatePicker: false,
      time: passedEditMoment
        ? new moment(passedEditMoment.sortStamp)
        : new Date(),
      date: passedEditMoment
        ? new moment(passedEditMoment.sortStamp)
        : new Date(),
      momentType: activeMomentType,
      momentFieldValues: passedEditMoment || mappedDefaultValues || {},
      attachedMedia: passedAttachedMedia || [],
      taggedPeople:
        (passedEditMoment && passedEditMoment.taggedPeople) ||
        passedTaggedPeople ||
        [],
      user: props.currentUser,
      passedEditMoment,
      showMomentTypeButton: !passedEditMoment,
      portfolioObservations:
        passedEditMoment &&
        passedEditMoment.momentType == 'portfolio' &&
        passedEditMoment.portfolioAssessments,
      showDialog: false,
      dialogTitle: 'Confim',
      dialogDescription: 'data',
      dialogText: '',
      dialogType: null,
      saving: false,
      isAttendingYes: activeMomentType == 'informedArrival',
      showOptions: false,
    };

    if (
      passedEditMoment &&
      passedEditMoment.momentType == 'food' &&
      passedEditMoment.foodItems &&
      (passedEditMoment.foodItems || []).length > 0
    ) {
      this.state.foodMomentItems = passedEditMoment.foodItems.map(
        fi => fi.name,
      );
      for (let i = 0; i < passedEditMoment.foodItems.length; i++) {
        const mfvTag = `foodItems_${i}`;
        this.state.momentFieldValues[mfvTag] =
          passedEditMoment.foodItems[i].amount;
      }
    }

    this.smsAlert = null;
    this.currentOrg = null;
    this.people = null;
    this.staffPeople = null;
    this.food = null;
    this.groups = null;
    this.currentUser = null;
    this.userPerson = null;
  }

  componentDidMount() {
    console.log(this.props.route?.params);
    const {tabIndex} = this.state;
    InteractionManager.runAfterInteractions(() => {
      getPersonIdForMomentEntry(tabIndex).then(personId => {
        Meteor.call('getCheckedInStaff', (err, result) => {
          const currentUser = Meteor.user();
          const userPerson =
            currentUser && People.findOne({_id: currentUser.personId});
          this.userPerson = userPerson;
          const {attributionPerson, taggedPeople} = this.state;
          const stateObj = {availableStaffAdmins: result || []};

          //find the existing attribution from result set
          let presetAttribution = _.find(result || [], {
            _id: attributionPerson._id,
          });

          //if we didn't find presetAttribution, check to see if the current person is and set own identity
          if (
            !presetAttribution &&
            userPerson &&
            userPerson._id == attributionPerson._id
          ) {
            presetAttribution = {
              _id: userPerson._id,
              firstName: userPerson.firstName,
              lastName: userPerson.lastName,
            };
          }

          if (presetAttribution) stateObj.attributionPerson = presetAttribution;

          // we only want to set the personId from navState if taggedPeople is empty (editing a moment on the person's profile should not delete moments for others)
          if (personId && _.isEmpty(taggedPeople))
            stateObj.taggedPeople = [personId];
          this.setState(stateObj);
        });
      });
    });
  }

  dismissKeyboard() {
    Keyboard.dismiss();
  }

  cancelDialog = () => {
    this.setState({
      saving: false,
      showDialog: false,
      dialogTitle: 'confirm',
      dialogDescription: 'data',
      dialogText: '',
      dialogType: null,
    });
  };
  preSave = ({currentOrg}) => {
    this.setState({saving: true});
    const momentDefinitions = new MomentDefinitions(this.state.momentType, {
        org: currentOrg,
      }),
      momentDefinition = momentDefinitions.getDefinition(),
      momentFieldValues = this.state.momentFieldValues;

    if (momentDefinition.fields) {
      const fieldValidationCheck = momentDefinition.fields.some(field => {
        if (field.validation) {
          return field.validation.some(item => {
            if (item.mandatory && !momentFieldValues[field.dataId]) {
              Alert.alert('Required Fields Missing', item.message);
              return true;
            }
          });
        }
      });
      if (fieldValidationCheck) {
        this.setState({saving: false});
        return;
      }
    }

    if (momentDefinition && momentDefinition.validationRules) {
      if (_.includes(momentDefinition.validationRules, 'alertValidation')) {
        if (
          !(
            momentFieldValues['alertSendTypeEmail'] ||
            momentFieldValues['alertSendTypeText'] ||
            momentFieldValues['alertSendTypePush']
          )
        ) {
          alert('You must select at least one delivery type for alert.');
          this.setState({saving: false});
          return;
        }

        if (
          !(
            momentFieldValues['tagOnlyCheckins'] ||
            momentFieldValues['tagEntireOrg']
          ) &&
          (!this.state.taggedPeople || this.state.taggedPeople.length == 0)
        ) {
          alert(
            'You must tag a person or select an option to send to checkedin people / everyone',
          );
          this.setState({saving: false});
          return;
        }
      }

      if (
        _.includes(momentDefinition.validationRules, 'smsTextValidation') &&
        this.smsAlert == null &&
        momentFieldValues['alertSendTypeText']
      ) {
        const prefix =
          currentOrg?.whiteLabel?.smsPrefix ?? currentOrg?.getLongName?.();
        const postfix = currentOrg?.whiteLabel?.smsPostfix ?? '... mp://open';
        const smsBody = `${prefix}: ${momentFieldValues['comment']} ${postfix}`;
        const parsedMessage = parseSegmentedMessage(smsBody);
        this.setState({
          showDialog: true,
          dialogTitle: 'Confirm SMS Notification Text',
          dialogDescription:
            'Customize and submit the notificaiton text sent to users for this alert',
          dialogText: parsedMessage.str,
          dialogType: 'smsAlert',
        });
        return;
      }
    } else {
      if (!this.state.taggedPeople || this.state.taggedPeople.length == 0) {
        alert('You must tag at least one person to save the moment.');
        this.setState({saving: false});
        return;
      }
    }
    this.onSave({currentOrg});
  };
  saveDialog = () => {
    const {dialogType, dialogText} = this.state;
    if (dialogType === 'smsAlert') {
      this.smsAlert = dialogText;
      this.setState({
        showDialog: false,
      });
    }

    this.preSave({currentOrg: this.currentOrg});
  };

  onChangeDialogText = val => {
    const {dialogType} = this.state;
    if (dialogType == 'smsAlert') {
      const parsedMessage = parseSegmentedMessage(val);
      this.setState({dialogText: parsedMessage.str});
    } else {
      this.setState({dialogText: val});
    }
  };

  hasCustomStaffPay = () => {
    // only show pay selector if it already exists on the checkin field and the user is an admin;
    // staff/admin can checkin thru the modal and self-select payType
    // const { currentOrg, userPerson } = this.props;
    const {momentFieldValues} = this.state;
    return (
      this.currentOrg &&
      this.userPerson &&
      this.userPerson?.type == 'admin' &&
      this.currentOrg.hasCustomization('people/staffPay/enabled') &&
      momentFieldValues['selectedPayTypeId']
    );
  };

  onCancel = () => {
    this.props.navigation.dispatch(CommonActions.goBack());
  };

  onMomentSelected = moment => {
    this.setState({momentType: moment?.type, showMomentPicker: false});
  };

  onDatePress = () => {
    this.setState({showDatePicker: !this.state.showDatePicker});
  };

  onDateConfirm = datetime => {
    this.setState({date: datetime, showDatePicker: false});
  };

  onTimePress = () => {
    this.setState({showTimePicker: !this.state.showTimePicker});
  };

  onTimeConfirm = datetime => {
    this.setState({time: datetime, showTimePicker: false});
  };

  onSetState = newState => {
    this.setState(newState);
  };

  onSave = ({currentOrg}) => {
    const momentDefinitions = new MomentDefinitions(this.state.momentType, {
      org: currentOrg,
    });
    const momentDefinition = momentDefinitions.getDefinition();
    const momentFieldValues = this.state.momentFieldValues;

    if (this.state.momentType == 'informedArrival') {
      let mappedFields = {};

      let requiredError = false;
      let requiredMessage = 'The following fields are required: ';
      _.each(momentDefinition.fields, field => {
        mappedFields[field.dataId] = this.state.momentFieldValues[field.dataId];
        const testStr = mappedFields[field.dataId]
          ? mappedFields[field.dataId]
          : '';
        if (field.required && testStr.trim().length == 0) {
          requiredError = true;
          requiredMessage += `${field.label} | `;
        }
      });

      const attendingValue = mappedFields['attending'] || 'Yes';
      if (attendingValue.startsWith('No')) {
        const absentComment = mappedFields['absentComment'] || '';
        mappedFields.attending = false;
        mappedFields.attendingAbsenceReason =
          attendingValue.replace('No - ', '').toLowerCase() +
          (absentComment.trim() ? ' : ' + absentComment.trim() : '');
      } else {
        if (requiredError) {
          Alert.alert('Required Fields Missing', requiredMessage);
          this.setState({saving: false});
          return;
        }
        mappedFields.attending = true;
      }

      let familyCheckinData = {
        personId: this.state.taggedPeople[0],
        checkinFormFields: mappedFields,
      };
      Meteor.call('familyCheckin', familyCheckinData, (error, result) => {
        if (error) {
          Alert.alert('Issue with check-in', error.reason);
          this.setState({saving: false});
        } else {
          this.props.navigation.dispatch(CommonActions.goBack());
        }
      });
    } else {
      let momentItem = {
        date: moment(this.state.date).format('M/D/YYYY'),
        momentType: this.state.momentType,
        attachedMedia: this.state.attachedMedia,
        taggedPeople: this.state.taggedPeople.map(p => {
          return (typeof p !== 'object' && p.includes('|')) ? p : 'person|' + p;
        }),
        user: {
          _id: Meteor.user()._id,
          orgId: Meteor.user().orgId,
        },
      };

      if (this.smsAlert != null) {
        momentItem.smsAlert = this.smsAlert;
      }

      if (!momentDefinition.hideTimePicker) {
        let timezone = Orgs.current().getTimezone();
        let unformattedTime = moment(this.state.time);
        momentItem.time = moment.tz(unformattedTime, timezone).format('h:mm a');
      }
      if (momentDefinition.isDynamic) {
        momentItem.isDynamic = true;
        momentItem['dynamicFieldValues'] = {};
      }

      _.each(momentDefinition.fields, field => {
        console.log('checking field', field);
        if (
          field.fieldType == 'stateSourcedFieldList' &&
          this.state[field.stateSource]
        ) {
          const stateSources = this.state[field.stateSource];
          console.log('stateSources', stateSources);
          _.each(stateSources, (sourceName, stateIndex) => {
            if (!momentItem[field.dataId]) momentItem[field.dataId] = [];
            const newEntry = {},
              stateValueKey = field.dataId + '_' + stateIndex;
            newEntry[field.fieldDefinition.itemName] = sourceName;
            newEntry[field.fieldDefinition.itemValueName] =
              this.state.momentFieldValues[stateValueKey];
            console.log('newEntry', newEntry);
            momentItem[field.dataId].push(newEntry);
          });
        } else if (field.fieldType == 'assessments') {
          momentItem['portfolioAssessments'] = this.state.portfolioObservations;
        } else {
          const key = field.dataId;
          const val = this.state.momentFieldValues[key];
          let outVal;
          if (typeof val === 'object' && !Array.isArray(val)) {
            outVal = val.fieldDataId || val.fieldValue || val.fieldLabel;
          } else outVal = val;
          if (momentDefinition.isDynamic)
            momentItem['dynamicFieldValues'][key] = outVal;
          else momentItem[key] = outVal;
        }
      });

      if (!momentDefinition.hideComment)
        momentItem.comment = this.state.momentFieldValues.comment;
      if (this.state.passedEditMoment)
        momentItem._id = this.state.passedEditMoment._id;

      momentItem.attributionPersonId = this.state.attributionPerson._id;

      console.log('New Moment Item: ', momentItem);

      Meteor.call('validateMoment', momentItem, (err, res) => {
        if (err) {
          Alert.alert('Moment Could Not Save', err.error);
          this.setState({saving: false});
        } else {
          SyncManager.add(momentItem);
          this.props.route?.params?.callback?.(this.state.taggedPeople);
          this.props.navigation.dispatch(CommonActions.goBack());
        }
      });
    }
  };
  onAttributionSelected = () => {
    const options = _.map(
      this.state.availableStaffAdmins,
      s => `${s.firstName} ${s.lastName}`,
    ).concat(['Remove Attribution', 'Cancel']);
    ActionSheet.show(
      {
        options,
        cancelButtonIndex: options.length - 1,
        title: 'Add Moment Attribution',
      },
      buttonIndex => {
        if (buttonIndex < options.length - 2) {
          this.setState({
            attributionPerson: this.state.availableStaffAdmins[buttonIndex],
          });
        } else if (buttonIndex == options.length - 2) {
          this.setState({
            attributionPerson: {},
          });
        }
      },
    );
  };

  toggleMomentPicker = () => {
    const userPerson = this.userPerson;
    const {momentType} = this.state;
    if (
      _.includes(['staff', 'admin'], userPerson?.type) &&
      !_.includes(['checkin', 'move', 'checkout'], momentType)
    ) {
      this.setState({showMomentPicker: !this.state.showMomentPicker});
    }
  };

  _addImageFunction = (selectedPeoples, image) => {
    var joined = this.state.attachedMedia.concat(image[0]);
    this.setState({attachedMedia: joined});
    this.setState({
      taggedPeople: selectedPeoples,
    });
  };

  showPhotoHandler = selectedType => {
    if (selectedType === undefined || selectedType === false) {
      ActionSheet.show(
        {
          options: ['Take Photo', 'Select from Gallery', 'Cancel'],
          cancelButtonIndex: 2,
          title: 'Add Media',
        },
        buttonIndex => {
          if (buttonIndex < 2) {
            this.showPhotoHandler(buttonIndex);
          }
        },
      );
    } else if (selectedType == 0) {
      const self = this;
      ImagePicker.openCamera({
        mediaType: 'any',
      })
        .then(image => {
          console.log('Response = ', image);
          image.type = image.mime;
          image.uri = image.path;
          // var joined = self.state.attachedMedia.concat(image);
          // self.setState({ attachedMedia: joined })
          this.props.navigation.navigate('ImageScreen', {
            newMomentType: 'comment',
            attachedMedia: [image],
            isFromAddMedia: true,
            addImageFunction: this._addImageFunction,
          });
        })
        .catch(e => {
          console.log(e);
        });
    } else {
      const options = {
        title: 'Select Photo or Video',
        mediaType: 'mixed',
        storageOptions: {
          skipBackup: true,
          path: 'images',
        },
        noData: true,
      };

      const self = this;

      ImagePicker.openPicker({multiple: true})
        .then(images => {
          console.log('Response = ', images);
          var joined = self.state.attachedMedia.concat(
            images.map(i => {
              i.type = i.mime;
              i.uri = i.path;
              return i;
            }),
          );
          self.setState({attachedMedia: joined});
        })
        .catch(e => {
          console.log(e);
        });
    }
  };

  showMomentTagger = () => {
    const people = People.find(
      {
        type: {$in: ['person', 'staff', 'admin']},
        inActive: {$ne: true},
      },
      {
        firstName: 1,
        lastName: 1,
        type: 1,
        checkInGroupId: 1,
        defaultGroupId: 1,
        checkedIn: 1,
        orgId: 1,
      },
    );
    const groups = Meteor.collection('groups').find({});
    this.props.navigation.navigate('MomentEntryMomentTagger', {
      people,
      groups,
      currentOrg: this.currentOrg,
      userPerson: this.userPerson,
      taggedPeople: this.state.taggedPeople,
      checkInNotRequired:
        noCheckInMoments.includes(this.state.momentType) &&
        this.currentOrg.hasCustomization('moments/postWhenCheckedOut/enabled'),
      onModalDismiss: taggedPeople => {
        this.setState({taggedPeople});
      },
    });
  };

  handleTaggerSave = response => {
    this.setState({showMomentTagger: false});
  };

  setMomentField = (fieldName, fieldValue) => {
    const userPerson = this.userPerson;
    //let newFieldValues = _.clone(this.state.momentFieldValues);
    //newFieldValues[fieldName] = fieldValue;
    const newFieldAssignment = {};
    if (fieldName == 'foodType') {
      let availableMeal;
      const filterGroupId = userPerson && userPerson.checkInGroupId;
      if (filterGroupId)
        availableMeal = _.find(this.food, foodEntry => {
          return (
            foodEntry.meal == fieldValue &&
            foodEntry.selectedGroups &&
            _.includes(foodEntry.selectedGroups, filterGroupId)
          );
        });
      if (!availableMeal)
        availableMeal = _.find(this.food, foodEntry => {
          return (
            foodEntry.meal == fieldValue &&
            (!foodEntry.selectedGroups || foodEntry.selectedGroups.length == 0)
          );
        });
      if (
        availableMeal &&
        (!this.state.momentFieldValues.comment ||
          this.state.momentFieldValues.comment == '')
      )
        newFieldAssignment['comment'] = availableMeal.description;
      if (availableMeal && availableMeal.foodItems) {
        this.setState({foodMomentItems: availableMeal.foodItems});
        console.log('found matching meal and set state');
      } else this.setState({foodMomentItems: null});
    }
    //this.setState({momentFieldValues: newFieldValues});

    newFieldAssignment[fieldName] = fieldValue;
    this.setState(prevState => ({
      momentFieldValues: Object.assign(
        {},
        prevState.momentFieldValues,
        newFieldAssignment,
      ),
    }));
    if (
      fieldName == 'attending' &&
      this.state.momentType == 'informedArrival'
    ) {
      this.setState({
        isAttendingYes: fieldValue == 'Yes',
      });
    }
    if (
      this.state.momentType == 'portfolio' &&
      fieldName == 'SelectMilestoneId'
    ) {
      this.setState({
        isMilestoneSelected: !this.state.isMilestoneSelected,
      });
    }
    console.log('set newField', newFieldAssignment);
  };

  getMomentField = fieldName => {
    const output = this.state.momentFieldValues[fieldName];
    return output;
  };

  getMomentFieldValue = fieldName => {
    const momentDefinitions = new MomentDefinitions(this.state.momentType, {
      org: this.currentOrg,
    });
    const momentDefinition = momentDefinitions.getDefinition();
    const fieldDefinition =
      momentDefinition &&
      momentDefinition?.fields?.find(f => f.dataId == fieldName);
    const fieldValueItem = this.state.momentFieldValues[fieldName];
    if (
      fieldDefinition &&
      fieldDefinition.fieldValuesQuery &&
      fieldDefinition.savedValue &&
      fieldValueItem &&
      typeof fieldValueItem !== 'object'
    ) {
      const savedValueItem =
        this.state.momentFieldValues[fieldDefinition.savedValue];
      const savedValueLabel =
        savedValueItem && savedValueItem[fieldDefinition.savedValueLabel];
      return savedValueLabel || 'N/A';
    } else if (this.state.momentType === 'illness') {
      return this.state.passedEditMoment?.illnessSymptomDisplayString();
    } else if (typeof fieldValueItem === 'object') {
      console.log(typeof fieldValueItem, fieldValueItem);
      return fieldValueItem.fieldValueLabel || fieldValueItem.fieldValue;
    } else return fieldValueItem;
  };

  addPortfolioObservation = () => {
    if (
      this.state.selectedStandard == null ||
      this.state.selectedObservation == null
    )
      return;
    const newObservation = {
      standardId: this.state.selectedStandard,
      value: this.state.selectedObservation,
    };
    this.setState(prevState => ({
      portfolioObservations: [
        ...(prevState.portfolioObservations || []),
        newObservation,
      ],
      selectedStandard: null,
      selectedStandardValue: null,
      selectedObservation: null,
    }));
  };

  removePortfolioObservation = idx => {
    const newObservations = [...this.state.portfolioObservations];
    newObservations.splice(idx, 1);
    this.setState({portfolioObservations: newObservations});
  };

  formatCurrentAmount = fieldId => {
    const data = this.getMomentField(fieldId);
    if (data) {
      return data + ' oz.';
    } else return 'Choose...';
  };

  _convertToDate = fieldValue => {
    if (typeof fieldValue == 'string' || fieldValue instanceof String) {
      return new moment(fieldValue, 'h:mm a').toDate();
    } else return new moment(fieldValue).toDate();
  };

  activateAmountPicker = fieldId => {
    NativePicker.init({
      pickerTitleText: 'Select amount',
      pickerConfirmBtnText: 'Confirm',
      pickerCancelBtnText: 'Cancel',
      pickerData: [
        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
        ['0', '1/4', '1/2', '3/4'],
      ],
      onPickerConfirm: data => {
        const decimalMap = {0: 0, '1/4': 0.25, '1/2': 0.5, '3/4': 0.75};
        this.setMomentField(
          fieldId,
          parseFloat(data[0]) + parseFloat(decimalMap[data[1]]),
        );
      },
    });
    NativePicker.show();
  };

  confirmTimePicker = (fieldId, datetime, asString) => {
    this.displayTimePicker(fieldId, false);
    this.setMomentField(
      fieldId,
      asString ? new moment(datetime).format('h:mm a') : datetime,
    );
  };

  displayTimePicker = (fieldId, val) => {
    let mutation = {},
      mutationLabel = 'showTimePicker' + fieldId;
    mutation[mutationLabel] = val;
    this.setState(mutation);
  };

  cancelTimePicker = fieldId => {
    this.displayTimePicker(fieldId, false);
  };

  setTimeFieldNow = fieldId => {
    let currentDate = this.state.time;
    if (fieldId === 'endTime') {
      currentDate = new Date();
    }
    let timezone = Orgs.current().getTimezone();
    let unformattedTime = moment(currentDate);
    let time = moment.tz(unformattedTime, timezone).format('h:mm a');
    this.setMomentField(fieldId, time);
  };

  handleMedicationsField = (field) => {
    const { taggedPeople } = this.state;
    console.log(taggedPeople)
    if (
      taggedPeople.length === 1 &&
      !taggedPeople[0].startsWith('group|')
    ) {
      const taggedId = taggedPeople[0]
      const taggedPerson = Meteor.collection('people').findOne(taggedId)
      const medications = taggedPerson?.currentMedications?.() || [];
      field.filteredFieldValues = medications?.map(m => ({
        fieldValue: m.name,
        fieldDataId: m._id || m.salesforceRecordId,
      }));
    }
  }

  _prepareTaggingData = () => {
    const { taggedPeople: taggedPeopleFromState } = this.state;
    const { taggedPeopleLabel: taggedPeopleLabelFromParams } = this.props.route.params || {};
    const People = Meteor.collection('people'); // Ensure People is accessible

    let calculatedIsStaffAdmin = false;
    const processedGroupIds = [];

    // Always use this.state.taggedPeople as the source for processing actual tags
    const peopleDataArray = taggedPeopleFromState || [];

    const individualLabels = peopleDataArray.map(tpid => {
      let currentLabel = '';
      const groupsForCurrentTpid = [];
      let personObject = null;

      if (typeof tpid === 'string') {
        if (tpid.includes('group|')) {
          const groupId = tpid.replace('group|', '');
          const g = _.find(this.groups, group => group._id == groupId);
          if (g) {
            groupsForCurrentTpid.push(groupId);
            currentLabel = g.name;
          } else {
            currentLabel = '';
          }
        } else if (tpid.includes('role|')) {
          const roleType = tpid.replace('role|', '');
          if (roleType === 'all_staff') {
            currentLabel = 'All Staff';
          } else {
            currentLabel = '';
          }
        } else {
          personObject = People.findOne(tpid);
          if (!personObject) {
            currentLabel = '';
          }
        }
      } else if (tpid && typeof tpid === 'object') {
        personObject = People.findOne(tpid);
        if (!personObject) {
            currentLabel = '';
        }
      } else if (tpid === null || tpid === undefined) {
        currentLabel = '';
      } else {
        currentLabel = '';
      }

      if (personObject) {
        const p = personObject;
        currentLabel = `${p.firstName} ${p.lastName}`;
        if (
          (p.type === 'staff' || p.type === 'admin') &&
          (this.state.momentType === 'checkin' || this.state.momentType === 'checkout')
        ) {
          calculatedIsStaffAdmin = true;
        }
        if (p.defaultGroupId) {
          groupsForCurrentTpid.push(p.defaultGroupId);
        }
        if (p.checkInGroupId) {
          groupsForCurrentTpid.push(p.checkInGroupId);
        }
      }
      
      if (groupsForCurrentTpid.length > 0) {
        processedGroupIds.push(...groupsForCurrentTpid);
      }
      
      return currentLabel;
    });

    const uniqueProcessedGroupIds = _.uniq(processedGroupIds.filter(id => id != null));

    let generatedDisplayLabel = individualLabels.filter(label => label && label.trim() !== '').join(', ');
    let finalDisplayLabel = generatedDisplayLabel; 

    if (typeof taggedPeopleLabelFromParams === 'string' && taggedPeopleLabelFromParams.trim().length > 0) {
      finalDisplayLabel = taggedPeopleLabelFromParams;
    }
    
    if (!finalDisplayLabel || finalDisplayLabel.trim() === '') {
      finalDisplayLabel = '+Another Person';
    }

    return {
      displayLabel: finalDisplayLabel,
      groupIds: uniqueProcessedGroupIds,
      isStaffAdmin: calculatedIsStaffAdmin,
    };
  }

  render() {
    const People = Meteor.collection('people');
    const currentUser = Meteor.user() || this.props.route.params?.userPerson;
    const currentOrgs = currentUser && Orgs.find({_id: currentUser.orgId})
    const userPerson = currentUser && People.findOne({_id: currentUser.personId})
    let selectedGroupsQuery = [{selectedGroups: []}];
    if (userPerson && (userPerson.checkInGroupId || userPerson.defaultGroupId))
      selectedGroupsQuery.push({
        selectedGroups: userPerson.checkInGroupId || userPerson.defaultGroupId,
      });

    this.people = People.find(
      {type: 'person', checkedIn: true},
      {sort: {lastName: 1, firstName: 1}},
    );
    this.staffPeople = People.find(
      {type: {$in: ['staff', 'admin']}},
      {sort: {lastName: 1, firstName: 1}},
    );
    this.food =
      Food.findWithRecurrence({query: {$or: selectedGroupsQuery}}) || [];
    this.groups = Meteor.collection('groups').find();
    this.currentOrg = currentOrgs && currentOrgs.length > 0 && currentOrgs[0];
    
    // Refactored tagging logic
    const { 
      displayLabel: taggedPeopleLabel, 
      groupIds, 
      isStaffAdmin 
    } = this._prepareTaggingData();
    this.taggedPerson = groupIds; // Update instance variable


    const ageGroup = Curriculums.activitiesAgeGroups();
    const ageGroupPortfolio = [];
    ageGroup &&
      ageGroup.map(ageGroupValue => {
        ageGroupPortfolio.push(ageGroupValue.label);
      });
    let availableMomentTypes = MomentDefinitions.availableMomentTypes(
        this.currentOrg,
      ),
      currentOrg = this.currentOrg,
      momentDefinitions = new MomentDefinitions(this.state.momentType, {
        org: currentOrg,
      }),
      momentDefinition = momentDefinitions.getDefinition(),
      currentGroup = (userPerson && userPerson.findCheckedInGroup && userPerson.findCheckedInGroup()) ?? null ,
      filterRules = {
        showBottleType: () => {
          return (
            currentOrg &&
            !currentOrg.hasCustomization('moments/food/hideBottle') &&
            (!currentOrg.hasCustomization('moments/food/infantGroupOptions') ||
              (currentGroup && currentGroup.typeInfant))
          );
        },
        showBabyFoodType: () => {
          return (
            currentOrg &&
            currentOrg.hasCustomization('moments/food/infantGroupOptions') &&
            currentGroup &&
            currentGroup.typeInfant
          );
        },
        showCerealType: () => {
          return (
            currentOrg &&
            currentOrg.hasCustomization('moments/food/infantGroupOptions') &&
            currentGroup &&
            currentGroup.typeInfant
          );
        },
        showFoodTubeAmount: () => {
          return this.getMomentField('foodType') == 'Tube';
        },
        showBottleAmount: () => {
          return this.getMomentField('foodType') == 'Bottle';
        },
        showBabyFoodAmount: () => {
          return this.getMomentField('foodType') == 'Baby Food';
        },
        showCerealAmount: () => {
          return this.getMomentField('foodType') == 'Cereal';
        },
        showFoodAmountPercent: () => {
          return (
            !_.includes(
              ['Tube', 'Bottle', 'Baby Food', 'Cereal'],
              this.getMomentField('foodType'),
            ) &&
            currentOrg &&
            currentOrg.hasCustomization('moments/food/showPercentage')
          );
        },
        showFoodAmount: () => {
          return (
            !_.includes(
              ['Tube', 'Bottle', 'Baby Food', 'Cereal'],
              this.getMomentField('foodType'),
            ) &&
            currentOrg &&
            !currentOrg.hasCustomization('moments/food/showPercentage') &&
            !((this.state.foodMomentItems || []).length > 0)
          );
        },
        medicalAllowFreeEntry: () => {
          return !(
            currentOrg &&
            currentOrg.hasCustomization('moments/medical/useProfileMedications')
          );
        },
      };

    if (!momentDefinition) return <LoadingScreen />;

    availableMomentTypes = _.filter(availableMomentTypes, t => {
      if (t.adminOnly) {
        return userPerson?.type == 'admin' ? true : false;
      }
      return true;
    });

    let filteredFields = _.filter(momentDefinition.fields, field => {
      if (
        field.customization &&
        currentOrg &&
        !currentOrg.hasCustomization(field.customization)
      )
        return false;
      if (
        field.langugages &&
        !_.includes(field.languages, currentOrg.language || '')
      )
        return false;
      if (field.displayRule && filterRules[field.displayRule])
        return filterRules[field.displayRule]();
      return true;
    });

    // moment-specific updating for food, curriculum
    let expandedFields = [];
    filteredFields.forEach((field, fieldIndex) => {
      if (field.fieldType == 'stateSourcedFieldList') {
        const stateData = this.state[field.stateSource];
        if (stateData && Array.isArray(stateData)) {
          stateData.forEach((stateItem, stateIndex) => {
            const newField = _.clone(field.fieldDefinition);
            newField.dataId = field.dataId + '_' + stateIndex;
            newField.label = stateItem;
            expandedFields.push(newField);
          });
        }
      } else expandedFields.push(field);
    });
    filteredFields = expandedFields;

    filteredFields.forEach(field => {
      if (field.fieldValues) {
        field.filteredFieldValues = _.filter(field.fieldValues, fieldValue => {
          if (typeof fieldValue === 'object' && fieldValue.displayRule)
            return filterRules[fieldValue.displayRule]();
          else if (typeof fieldValue === 'object' && fieldValue.customization)
            return (
              currentOrg &&
              currentOrg.hasCustomization(fieldValue.customization)
            );
          else return true;
        });
      }
      if (field.fieldValuesQuery) {
        if (field.fieldValuesQuery == 'activityTypes') {
          let options = [];
          if (
            currentOrg &&
            currentOrg.hasCustomization('moments/activity/childDefaults')
          )
            options = [
              '',
              'Table Times',
              'Outdoor Play',
              'Ball Play',
              'Table Toys',
              'Tummy Time',
              'Dance',
            ];
          else if (
            currentOrg &&
            currentOrg.hasCustomization(
              'moments/activity/organizationSpecificTypes',
            )
          )
            options = currentOrg.valueOverrides
              ? currentOrg.valueOverrides.activityTypes
              : [];
          else
            options = [
              '',
              'Beauty/Barber Shop',
              'Cognitive Games',
              'Daily Living Skills',
              'Discussion/Reminisce/Read',
              'Entertainment/Speakers',
              'Exercise',
              'Fine Motor',
              'Gross Motor',
              'Intergenerational',
              'Music',
              'Outings',
              'Pet Therapy',
              'Shower',
              'Social',
              'Spiritual',
              'Walking',
              'Other',
            ];
          field.filteredFieldValues = options.sort();
        } else if (field.fieldValuesQuery == 'illnessSymptoms') {
          field.filteredFieldValues = [
            'Fever (101 degrees or higher)',
            'Eyes (green/yellow discharge/inflamation of the eye)',
            'Diarrhea (runny, watery, bloody stools or2 or more loose stools within 24 hours)',
            'Vomitting (2 or more times within 24 hours)',
            'Ears (pain/ear drainage, or child rubs/pulls ears)',
            'Unusual spots/rashes/blisters (present and/or spreadeing over a period of time)',
            'Head Lice (children with lice will be referred for treatment)',
            'Other (describe in Comment box)',
          ];
        } else if (field.fieldValuesQuery == 'supplyTypes') {
          let options = [];
          if (currentOrg && currentOrg.language == 'translationsEnChildCare')
            options = [
              '',
              'Breast Milk',
              'Clothing',
              'Diapers',
              'Formula',
              'Pull Ups',
              'Undergarments',
              'Wipes',
            ];
          else
            options = [
              'Full change of clothes',
              'Slacks',
              'Underwear',
              "Return center's clothes",
              'Pads/Depends',
              'Other',
            ];
          if (
            currentOrg &&
            currentOrg.hasCustomization(
              'moments/supplies/organizationSpecificTypes',
            )
          )
            options = currentOrg.valueOverrides
              ? currentOrg.valueOverrides.suppliesTypes
              : [];
          field.filteredFieldValues = options;
          field.selectType = 'multiple';
        } else if (field.fieldValuesQuery == 'curriculums') {
          const startRange = new moment().startOf('day').valueOf(),
            endRange = new moment(startRange).add(1, 'day').valueOf();
          let query = {
            $and: [{scheduledDate: {$gte: startRange, $lt: endRange}}],
          };
          var selectedGroupsQuery = {$or: [{selectedGroups: []}]};
          if (currentGroup || this.taggedPerson.length > 0)
            if (this.taggedPerson.length > 0) {
              this.taggedPerson.forEach(id =>
                selectedGroupsQuery['$or'].push({selectedGroups: id}),
              );
            } else {
              selectedGroupsQuery['$or'].push({
                selectedGroups: currentGroup._id,
              });
            }
          query['$and'].push(selectedGroupsQuery);
          const curriculums = Meteor.collection('curriculum').find(query);

          field.filteredFieldValues = _.map(curriculums, c => {
            c.fieldValue = c.headline;
            c.fieldDataId = c._id;
            return c;
          });
        } else if (field.fieldValuesQuery == 'curriculumTypes') {
          const types =
            currentOrg && currentOrg.availableCurriculumTypes({mapped: true});

          field.filteredFieldValues = _.map(types, v => {
            return {fieldDataId: v, fieldValue: v.replace('`', ' > ')};
          });
        } else if (field.fieldValuesQuery == 'medicineTypes') {
          if (
            currentOrg &&
            currentOrg.hasCustomization(
              'moments/medical/organizationSpecificTypes',
            )
          ) {
            options = currentOrg.valueOverrides
              ? currentOrg.valueOverrides.medicineTypes
              : [];
            field.filteredFieldValues = options;
          }
        } else if (field.fieldValuesQuery == 'staffPeople') {
          field.filteredFieldValues =
            this.staffPeople &&
            _.map(this.staffPeople, p => {
              return {
                fieldValue: p.firstName + ' ' + p.lastName,
                fieldDataId: p._id,
              };
            });
        } else if (field.fieldValuesQuery === 'medications') {
          this.handleMedicationsField(field)
        }
      }
    });

    let availablePortfolioStandards = [],
      currentAssessments = [];
    const portfolioCurriculum = this.getMomentField('portfolioCurriculumId'),
      availableObservationLevels =
        Orgs.current() && Orgs.current().getAvailableAssessmentLevels(),
      ageRangeSelected = this.getMomentField('portfolioAgeRangeId');
    if (this.state.momentType == 'portfolio') {
      if (
        (!this.state.isMilestoneSelected ||
          !ageRangeSelected ||
          ageRangeSelected?.fieldValue == 'IGNORE_FIELD') &&
        portfolioCurriculum
      ) {
        const portfolioCurriculumId = portfolioCurriculum._id,
          curriculum = Curriculums.findOne(
            portfolioCurriculumId ? portfolioCurriculumId : '',
          );
        if (curriculum)
          availablePortfolioStandards =
            curriculum.findStandardsRaw().map(standard => ({
              id: standard && standard.standardId,
              label: standard.category + ': ' + standard.benchmark,
              value: standard.category + ': ' + standard.benchmark,
              category: standard.category,
            })) || [];
      } else {
        // If there's no portfolio activity selected then we want to allow all possible observations for the age group
        // of the group that the staff/admin is checked into
        Curriculums.getStandards().map(standard => {
          if (
            this.state.isMilestoneSelected &&
            ageRangeSelected &&
            ageRangeSelected?.fieldValue != 'IGNORE_FIELD'
          ) {
            if (ageRangeSelected && ageRangeSelected == standard.ageGroup) {
              availablePortfolioStandards.push({
                id: standard && standard.standardId,
                label: standard.category + ': ' + standard.benchmark,
                value: standard.category + ': ' + standard.benchmark,
                category: standard.category,
              });
            }
          } else {
            if (
              currentGroup &&
              currentGroup.activitiesAgeGroup == standard.ageGroup
            ) {
              availablePortfolioStandards.push({
                id: standard && standard.standardId,
                label: standard.category + ': ' + standard.benchmark,
                value: standard.category + ': ' + standard.benchmark,
                category: standard.category,
              });
            }
          }
        });
      }
      availablePortfolioStandards = _.chain(availablePortfolioStandards)
        .sortBy('category')
        .value();
      [
        {
          label:
            Platform.OS == 'android' ? 'Choose One' : 'Choose milestone...',
          value:
            Platform.OS == 'android' ? 'Choose One' : 'Choose milestone...',
          id: 'null',
          category: '',
        },
      ].concat(availablePortfolioStandards);
      const assessments = this.state.portfolioObservations,
        allStandards = Curriculums.getStandards();
      let i = 0;

      _.each(assessments, assessment => {
        const matchedStandard = _.find(
            allStandards,
            s => s.standardId == assessment.standardId,
          ),
          matchedAssessmentLevel = _.find(
            availableObservationLevels,
            l => l.value == assessment.value,
          );

        if (matchedStandard && matchedAssessmentLevel)
          currentAssessments.push({
            label:
              (matchedStandard.category
                ? matchedStandard.category
                : matchedStandard.source) +
              ': ' +
              matchedStandard.benchmark,
            value: assessment.value,
            valueLabel: matchedAssessmentLevel.label,
            pos: i,
          });
        i = i + 1;
      });
    }

    let showCheckinPay = false,
      checkinPayTypes = [];
    if (this.hasCustomStaffPay()) {
      showCheckinPay = true;
      checkinPayTypes = currentOrg && currentOrg.availableCustomPayTypes();
    }

    const hideComment = _.get(momentDefinition, 'hideComment', false);
    const hideMediaPicker = _.get(momentDefinition, 'hideMomentPicker', false);
    const required = _.get(momentDefinition, 'required', false);
    const {navigation, route} = this.props;
    return (
      <Container testID="momententry-modal">
        <Dialog.Container
          visible={this.state.showDialog}
          onBackdropPress={() => this.dismissKeyboard()}>
          <Dialog.Title style={{color: colors.black}}>
            {this.state.dialogTitle}
          </Dialog.Title>
          <Dialog.Description>
            {this.state.dialogDescription}
          </Dialog.Description>
          <Dialog.Input
            value={this.state.dialogText}
            onChangeText={val => this.onChangeDialogText(val)}
            wrapperStyle={styles.dialogInput}
            style={[{flex: 1, flexWrap: 'wrap'}]}
            multiline
          />
          <Dialog.Button
            label="Cancel"
            onPress={() => {
              this.cancelDialog();
            }}
          />
          <Dialog.Button
            label="Save"
            onPress={() => {
              this.props?.route?.params?.onSave
                ? this.props?.route?.params?.onSave()
                : this.saveDialog();
            }}
          />
        </Dialog.Container>
        {this.state.showMomentPicker && (
          <MomentPicker
            testID="momentpicker-selectType"
            visible={this.state.showMomentPicker}
            availableMomentTypes={availableMomentTypes}
            onButtonPress={this.onMomentSelected}
            onClose={this.onCancel}
          />
        )}
        {!this.state.showMomentPicker && (
          <OptionalSafeAreaView>
            {isStaffAdmin && (
              <>
                <View
                  style={{
                    backgroundColor: 'black',
                    opacity: 0.1,
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    top: 0,
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: 1,
                  }}></View>
                <Button
                  transparent
                  onPress={this.onCancel}
                  style={{
                    position: 'absolute',
                    backgroundColor: 'transparent',
                    width: 20,
                    height: 20,
                    marginHorizontal: 20,
                    marginTop: 20,
                    zIndex: 2,
                  }}></Button>
              </>
            )}
            <Header
              showActions
              momentIcon={momentDefinition.icon}
              momentName={
                momentDefinition.overrideTitle ||
                `${momentDefinition.prettyName} Moment`
              }
              onClose={this.onCancel}
              onSave={() => {
                console.log('onSave');
                if (this.props?.route?.params?.onSave) {
                  this.props?.route?.params?.onSave({
                    momentType: this.state.momentType,
                    momentFieldValues: this.state.momentFieldValues,
                    taggedPeople: this.state.taggedPeople,
                    attachedMedia: this.state.attachedMedia,
                  });
                } else {
                  const people = this.state.taggedPeople.map(person => {
                    return People.findOne(
                      {_id: person},
                      {fields: {firstName: 1, lastName: 1, checkedIn: 1}},
                    );
                  });
                  preSave({
                    currentOrg,
                    setState: val => this.setState(val),
                    momentFieldValues: this.state.momentFieldValues,
                    taggedPeople: this.state.taggedPeople,
                    smsAlert: this.state.smsAlert,
                    momentType: this.state.momentType,
                    attachedMedia: this.state.attachedMedia,
                    navigation: this.props.navigation,
                    parseSegmentedMessage: parseSegmentedMessage,
                    onSave: this.onSave, // Temporary until we can refactor the save function
                    checkInNotRequired:
                      this.state.momentType === 'informedArrival' ||
                      (noCheckInMoments.includes(this.state.momentType) &&
                        this.currentOrg.hasCustomization(
                          'moments/postWhenCheckedOut/enabled',
                        )),
                    peopleData: people,
                  });
                }
              }}
              toggleMomentPicker={this.toggleMomentPicker}
              saving={this.state.saving}
            />
            <KeyboardAwareScrollView
              testID="momententry-scrollview"
              extraScrollHeight={150}
              contentContainerStyle={{marginHorizontal: 20}}>
              {isStaffAdmin && (
                <View>
                  <Text
                    style={{
                      color: '#da2e35',
                      fontSize: 18,
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}>
                    Log into the web app to edit time cards.
                  </Text>
                </View>
              )}
              {!_.get(momentDefinition, 'hidePeopleChooser', false) && (
                <MomentAttribution
                  onPress={this.onAttributionSelected}
                  attributionPerson={this.state.attributionPerson}
                  userPerson={userPerson}
                />
              )}
              <Tag
                showMomentTagger={this.showMomentTagger}
                taggedPeopleLabel={taggedPeopleLabel}
                hidePeopleChooser={
                  this.props.route.params?.hidePeopleChooser ||
                  _.get(momentDefinition, 'hidePeopleChooser', false)
                }
              />
              {!momentDefinition.hideDatePicker && (
                <DateField
                  isVisible={this.state.showDatePicker}
                  date={this.state.date}
                  onPress={this.onDatePress}
                  onConfirm={this.onDateConfirm}
                />
              )}
              {!momentDefinition.hideTimePicker && (
                <TimeField
                  isVisible={this.state.showTimePicker}
                  date={this._convertToDate(this.state.time)}
                  time={this.state.time}
                  onPress={this.onTimePress}
                  onConfirm={this.onTimeConfirm}
                />
              )}
              {filteredFields &&
                filteredFields.map(field => {
                  if (
                    this.state.momentType == 'informedArrival' &&
                    field.dataId != 'attending' &&
                    field.dataId != 'absentComment' &&
                    !this.state.isAttendingYes
                  ) {
                    return null;
                  } else if (
                    this.state.momentType == 'informedArrival' &&
                    field.dataId == 'absentComment' &&
                    this.state.isAttendingYes
                  ) {
                    return null;
                  }
                  if (
                    this.state.momentType == 'portfolio' &&
                    field.dataId == 'portfolioAgeRangeId'
                  ) {
                    field.fieldValues =
                      Platform.OS == 'ios'
                        ? [
                            {
                              fieldValueLabel: 'Choose One...',
                              fieldValue: 'IGNORE_FIELD',
                            },
                          ].concat(ageGroupPortfolio)
                        : ageGroupPortfolio;
                  }
                  if (
                    this.state.momentType == 'portfolio' &&
                    (field.dataId == 'portfolioAgeRangeId' ||
                      field.dataId == 'portfolioMilestoneId') &&
                    !this.state.isMilestoneSelected
                  ) {
                    return null;
                  }
                  switch (field.fieldType) {
                    case 'buttons':
                      return (
                        <ButtonsField
                          field={field}
                          getMomentField={this.getMomentField}
                          setMomentField={this.setMomentField}
                          required={required}
                        />
                      );
                    case 'checkbox':
                      return (
                        <CheckBoxField
                          field={field}
                          getMomentField={this.getMomentField}
                          setMomentField={this.setMomentField}
                          required={required}
                        />
                      );
                    case 'amount':
                      return (
                        <AmountField
                          field={field}
                          activateAmountPicker={this.activateAmountPicker}
                          formatCurrentAmount={this.formatCurrentAmount}
                          required={required}
                        />
                      );
                    case 'select':
                      if (field.selectType == 'multiple')
                        return (
                          <SelectMultiple
                            field={field}
                            getMomentField={this.getMomentField}
                            getMomentFieldValue={this.getMomentFieldValue}
                            setMomentField={this.setMomentField}
                            passedEditMoment={this.state.passedEditMoment}
                            required={required}
                          />
                        );
                      else
                        return (
                          <SelectField
                            field={field}
                            getMomentField={this.getMomentField}
                            getMomentFieldValue={this.getMomentFieldValue}
                            setMomentField={this.setMomentField}
                            passedEditMoment={this.state.passedEditMoment}
                            required={required}
                          />
                        );
                    case 'string':
                      return (
                        <StringField
                          field={field}
                          getMomentField={this.getMomentField}
                          setMomentField={this.setMomentField}
                          hideStringPrompt={_.get(
                            momentDefinition,
                            'hideStringPrompt',
                            false,
                          )}
                          required={required}
                        />
                      );
                    case 'text':
                      return (
                        <TextField
                          testID={field?.dataId}
                          field={field}
                          getMomentField={this.getMomentField}
                          setMomentField={this.setMomentField}
                          required={required}
                        />
                      );
                    case 'timePicker':
                      return (
                        <TimePickerField
                          field={field}
                          isVisible={
                            this.state['showTimePicker' + field.dataId]
                          }
                          getMomentField={this.getMomentField}
                          displayTimePicker={this.displayTimePicker}
                          confirmTimePicker={this.confirmTimePicker}
                          cancelTimePicker={this.cancelTimePicker}
                          convertToDate={this._convertToDate}
                          setTimeFieldNow={this.setTimeFieldNow}
                          required={required}
                        />
                      );
                    case 'assessments':
                      return (
                        <AssessmentsField
                          field={field}
                          onSetState={this.onSetState}
                          removePortfolioObservation={
                            this.removePortfolioObservation
                          }
                          selectedStandard={this.state.selectedStandard}
                          selectedStandardValue={
                            this.state.selectedStandardValue
                          }
                          selectedObservation={this.state.selectedObservation}
                          addPortfolioObservation={this.addPortfolioObservation}
                          availableObservationLevels={
                            availableObservationLevels
                          }
                          currentAssessments={currentAssessments}
                          availablePortfolioStandards={
                            availablePortfolioStandards
                          }
                        />
                      );
                    default:
                      return null;
                  }
                })}
              {showCheckinPay && (
                <PayTypeField
                  onPayTypeValueChange={val =>
                    this.setMomentField('selectedPayTypeId', val)
                  }
                  selectedPayTypeId={this.getMomentFieldValue(
                    'selectedPayTypeId',
                  )}
                  checkinPayTypes={checkinPayTypes}
                />
              )}
              {!hideComment && (
                <CommentField
                  setMomentField={this.setMomentField}
                  comment={this.state.momentFieldValues.comment}
                />
              )}
              {!hideMediaPicker && !this.state.hideAddMediaButton && (
                <MediaField
                  attachedMedia={this.state.attachedMedia}
                  showPhotoHandler={() => {
                    this.setState({showOptions: true});
                  }}
                />
              )}
            </KeyboardAwareScrollView>
          </OptionalSafeAreaView>
        )}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.showOptions}
          onRequestClose={() => this.setState({showOptions: false})}>
          <TouchableWithoutFeedback
            onPress={() => this.setState({showOptions: false})}>
            <View
              style={{
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                position: 'absolute',
              }}
            />
          </TouchableWithoutFeedback>
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View
              style={{
                width: 300,
                padding: 20,
                backgroundColor: 'white',
                borderRadius: 10,
              }}>
              <TouchableOpacity
                onPress={() => {
                  const handleImage = res => {
                    const attachedMedia = [...this.state.attachedMedia, ...res];
                    this.setState({
                      attachedMedia: attachedMedia,
                      showOptions: false,
                    });
                  };
                  imagePicker(null, route, 'camera', handleImage);
                }}>
                <Text style={{padding: 10}}>Take Photo</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  imagePicker(navigation, route, 'gallery').then(res => {
                    const attachedMedia = [...this.state.attachedMedia, ...res];
                    this.setState({
                      attachedMedia: attachedMedia,
                      showOptions: false,
                    });
                  });
                }}>
                <Text style={{padding: 10}}>Select from Gallery</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => this.setState({showOptions: false})}>
                <Text style={{padding: 10}}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </Container>
    );
  }
}

export default MomentEntry;
