import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Nucleo from '../../../components/icons/Nucleo';
import colors from '../../../config/colors.json';
import _ from 'lodash';

const styles = {
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 12,
  },
  label: {
    color: colors.primaryA,
    fontSize: 20,
    fontWeight: 'bold'
  },
  tagList: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10
  },
};

export default (props) => (
  <View style={styles.container}>
    <Text style={styles.label}>Attribution</Text>
    <View style={styles.tagList}>
      <TouchableOpacity style={{flex: 1}} onPress={props.onPress}>
        {
          _.isEmpty(props.attributionPerson) ?
          (
            <Text style={{paddingLeft: 5}}>{`${props.userPerson.firstName} ${props.userPerson.lastName}`}</Text>
          ) : 
          (
            <Text style={{paddingLeft: 5}}>{`${props.attributionPerson.firstName} ${props.attributionPerson.lastName}`}</Text>
          )
        }
      </TouchableOpacity>
        <TouchableOpacity style={{alignItems: 'center', justifyContent: 'center'}} onPress={props.onPress}>
          <Nucleo
            name="icon-small-right"
            size={24}
            color={colors.primaryA}
          />
        </TouchableOpacity>
    </View>
  </View>
)
