import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Nucleo from '../../../components/icons/Nucleo';
import colors from '../../../config/colors.json';

const styles = {
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginVertical: 12,
  },
  label: {
    color: colors.primaryA,
    fontSize: 20,
    fontWeight: 'bold'
  },
  tagList: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10
  },
};


export default (props) => (
  <View style={styles.container}>
    <Text style={styles.label}>Tag</Text>
    {
      !props.hidePeopleChooser ?
      (
        <View style={styles.tagList}>
          <TouchableOpacity style={{flex: 1}} onPress={props.showMomentTagger}>
            <Text style={{paddingLeft: 5}}>{props.taggedPeopleLabel}</Text>
          </TouchableOpacity>
            <TouchableOpacity style={{alignItems: 'center', justifyContent: 'center'}} onPress={props.showMomentTagger}>
              <Nucleo
                name="icon-small-right"
                size={24}
                color={colors.primaryA}
              />
            </TouchableOpacity>
        </View>
      ) :
      (
        <Text style={{paddingLeft: 5, paddingVertical: 6}}>{props.taggedPeopleLabel}</Text>
      )
    }
  </View>
)
