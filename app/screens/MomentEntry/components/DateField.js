import React from 'react';
import { View, Text, TouchableOpacity, Appearance } from 'react-native';
import moment from 'moment';
import DateTimePicker from 'react-native-modal-datetime-picker';
import Nucleo from '../../../components/icons/Nucleo';
import colors from '../../../config/colors.json';

const styles = {
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 12,
    padding: 10,
    backgroundColor: colors.lightGray,
    borderRadius: 4,
  },
  label: {
    color: colors.darkGray,
    fontSize: 20,
    fontWeight: 'bold',
  },
  dateView: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10
  },
};


export default (props) => (
  <View style={styles.container}>
    <Text style={styles.label}>Date</Text>
    <View style={styles.dateView}>
      <TouchableOpacity style={{flex: 1}} onPress={props.onPress}>
        <Text style={{paddingLeft: 5}}>{moment(props.date).format("M/D/YYYY")}</Text>
      </TouchableOpacity>
      <TouchableOpacity style={{alignItems: 'center', justifyContent: 'center'}} onPress={props.onPress}>
        <Nucleo
          name="icon-small-triangle-down"
          size={24}
          color={colors.darkGray}
        />
      </TouchableOpacity>
      <DateTimePicker
        testID={'date-picker'}
        isVisible={props.isVisible}
        onConfirm={props.onConfirm}
        onCancel={props.onPress}
        mode="date"
        isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
      />
    </View>
  </View>
)
