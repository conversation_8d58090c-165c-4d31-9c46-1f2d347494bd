import React, { Component } from 'react';
import { TouchableWithoutFeedback, View, Linking, StyleSheet, FlatList, TouchableOpacity, Text, InteractionManager, Alert } from 'react-native';
import { Badge,Icon } from 'native-base';
import RBSheet from "react-native-raw-bottom-sheet";
import Meteor from 'react-native-meteor';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { connect } from 'react-redux';
import { CommonActions } from '@react-navigation/native';
import { withTranslation } from 'react-i18next';

import AccountSwitcher from '../../components/AccountSwitcher';
import { imagePicker } from '../../helpers/ImagePicker';
import Nucleo from '../../components/icons/Nucleo';
import colors from '../../config/colors.json';
import * as environmentSettings from '../../shared/Settings';
import _ from 'lodash';
import moment from 'moment';

import Intercom from '@intercom/intercom-react-native';

import { clearBadge, toggleOfflineMode } from '../../actions/userData';
import StaffDashboard from './StaffDashboard';
import {PERMISSIONS, check, request} from 'react-native-permissions'
import NotificationsHandler from "../../components/Notifications";
import { accountLongPress } from '../Offline/offlineUtil';

const styles = StyleSheet.create({
  badge: {
		position: "absolute",
		width:12,
		height:12,
		top: -2,
		right: -2,
		left: null,
		bottom: null
  }
});



class Dashboard extends Component {

  componentDidMount() {
    this.props.navigation.dispatch(
      CommonActions.setParams({
        showImagePicker: this._showImagePicker,
        showQrScanner: this._showQrScanner,
        showTray: this._showTray,
        accountLongPress: () => accountLongPress(this.props),
      }),
    );
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const appHelpLastChecked = nextProps.appHelpLastChecked;
    const appHelpBadgeDate = nextProps.appHelpBadgeDate;
    const previousBadge = this.props.route?.params?.badge;

    let badge = false;
    if (
      appHelpBadgeDate &&
      appHelpLastChecked &&
      new moment(appHelpBadgeDate).isAfter(new moment(appHelpLastChecked))
    ) {
      badge = true;
    } else if (appHelpBadgeDate && !appHelpLastChecked) {
      badge = true;
    }

    if (previousBadge != badge) {
      this.props.navigation.setParams({
        badge,
      });
    }
  }

  _showImagePicker = () => {
    const {navigation, route} = this.props;
    const userPerson = route?.params?.userPerson ?? {};
    imagePicker(navigation, userPerson);
  };

  _showQrScanner = (userPerson, currentOrg) => {
    const {navigation, route} = this.props;
    navigation.navigate('QRCheckInModal', {userPerson, currentOrg});
  };

  _showTray = () => {
    this.Scrollable.open();
  };

  _callAddChatSupport = (oldMenus, currentOrg) => {
    const chatSupportURL =
      currentOrg && _.get(currentOrg, 'chatSupportSettings.chatUrl', '');
    const chatSupportTitle =
      currentOrg &&
      _.get(
        currentOrg,
        'chatSupportSettings.menuVerbiage',
        this.props.t('tray.chatSupport'),
      );
    const chatSupportMenu = {
      _id: 'launchchatsupport',
      title: chatSupportTitle,
      icon: 'icon-f-chat',
      mobileLink: chatSupportURL,
    };
    const isFound = oldMenus.some(
      element => element._id === 'launchchatsupport',
    );
    !isFound && chatSupportURL && oldMenus.splice(3, 0, chatSupportMenu);
    return oldMenus;
  };

  onTrayItemPress = item => {
    const {dispatch} = this.props;
    if (item.clearBadge) {
      this.props.navigation.dispatch(
        CommonActions.setParams({
          badge: false,
        }),
      );
      dispatch(clearBadge());
    }

    if (item.mobileLink) {
      Linking.openURL(item.mobileLink);
    }

    if (item.mobileNavigation) {
      if (item.mobileNavigation == 'LegacyHelpWebView') {
        InteractionManager.runAfterInteractions(() => {
          this.props.navigation.navigate('HelpWebView');
        });
      } else if (item.mobileNavigation == 'HelpWebView') {
        Intercom.present();
      } else {
        InteractionManager.runAfterInteractions(() => {
          this.props.navigation.navigate(item.mobileNavigation);
        });
      }
    }

    this.Scrollable.close();
  };

  renderTrayItem = ({item}) => {
    const badge = this.props.route?.params?.badge;
    return (
      <TouchableOpacity
        style={{
          flex: 1,
          flexDirection: 'row',
          marginVertical: 16,
          alignItems: 'center',
        }}
        onPress={() => this.onTrayItemPress(item)}>
        <View>
          {item?._id == 'launchchatsupport' ? (
            <Icon
              type="Ionicons"
              name="chatbox-ellipses"
              style={{
                fontSize: 33,
                marginHorizontal: 14,
                color: colors.primaryA,
              }}
            />
          ) : badge && item.clearBadge && item.title == this.props.t('tray.whatsNew') ? (
            <Icon
              type={item.iconType}
              name={item.icon}
              style={{fontSize: 33, marginHorizontal: 14, color: 'deepskyblue'}}
            />
          ) : (
            <Icon
              type={item.iconType}
              name={item.icon}
              style={{
                fontSize: item.title == this.props.t('tray.learningPath') ? 25 : 33,
                marginHorizontal: 14,
                color: colors.primaryA,
              }}
            />
          )}
        </View>
        {badge && item.clearBadge && item.title == this.props.t('tray.whatsNew') ? (
          <Text style={{color: 'deepskyblue', fontSize: 18}}>{item.title}</Text>
        ) : (
          <Text style={{color: colors.black, fontSize: 18}}>{item.title}</Text>
        )}
      </TouchableOpacity>
    );
  };

  renderSeparator = () => (
    <View
      style={{
        flex: 1,
        backgroundColor: colors.grayDark,
        height: 0.5,
        marginLeft: 48,
      }}
    />
  );

  static navigationOptions = ({navigation, route}) => {
    function onShowProfileOptions() {
      const opt = route?.params?.showSwitcher ?? false;
      navigation.dispatch(CommonActions.setParams({showSwitcher: !opt}));
    }

    async function onShowHelp() {
      navigation.navigate('HelpWebView');
    }

    function requestPermissions() {
      request(PERMISSIONS.ANDROID.CAMERA)
        .then(result => {
          console.log('requestPermissions result', result);
        })
        .catch(err => {
          console.log('requestPermissions err', err);
        });
    }

    const userPerson = route?.params?.userPerson ?? {type: null};
    const badge = route?.params?.badge;
    const currentOrg = Orgs.findOne(userPerson?.orgId);
    const hasQrCustomization =
      currentOrg &&
      _.get(currentOrg, 'customizations.people/qrCodeCheckin/enabled', false);
    return {
      title: i18n.t('dashboard.headerTitle'),
      headerShadowVisible: false,
      headerLeft: props => (
        <TouchableWithoutFeedback onPress={route?.params?.showTray}>
          <View>
            <Nucleo name="icon-c-info" size={24} color={colors.primaryA} />
            {badge && <Badge style={styles.badge} />}
          </View>
        </TouchableWithoutFeedback>
      ),
      headerRight: props => (
        <View style={{flexDirection: 'row'}}>
          {_.includes(['staff', 'admin'], userPerson.type) && (
            <TouchableWithoutFeedback
              onPress={() => route.params.showImagePicker()}>
              <Nucleo name="icon-camera" size={24} color={colors.primaryA} />
            </TouchableWithoutFeedback>
          )}
          {hasQrCustomization && <View style={{width: 20}} />}
          {hasQrCustomization && (
            <TouchableWithoutFeedback
              onPress={() =>
                route?.params?.showQrScanner?.(userPerson, currentOrg)
              }>
              <Nucleo
                name="icon-barcode-qr"
                size={24}
                color={colors.primaryA}
              />
            </TouchableWithoutFeedback>
          )}
          <View style={{width: 20}} />
          <TouchableWithoutFeedback
            testID="account-icon"
            onPress={() => {
              requestAnimationFrame(() => {
                onShowProfileOptions();
              });
            }}
            onLongPress={route?.params?.accountLongPress}>
            <Nucleo name="icon-circle-10" size={24} color={colors.primaryA} />
          </TouchableWithoutFeedback>
        </View>
      ),
    };
  };

  render() {
    const {appHelpInfo} = this.props;
    const userPerson = this.props.route?.params?.userPerson ?? {type: null};
    const currentOrg = Orgs.findOne(userPerson?.orgId);
    const hasChatSupportCustomization =
      currentOrg &&
      _.get(currentOrg, 'customizations.people/chatSupport/enabled', false);
    const appHelpInfoNew = hasChatSupportCustomization
      ? this._callAddChatSupport(appHelpInfo, currentOrg)
      : appHelpInfo;
    return (
      <View style={{flex: 1, backgroundColor: colors.white}}>
        <AccountSwitcher
          navigation={this.props.navigation}
          route={this.props.route}
          isVisible={this.props.route.params?.showSwitcher}
        />
        <NotificationsHandler />
        <StaffDashboard
          navigation={this.props.navigation}
          route={this.props.route}></StaffDashboard>

        <RBSheet
          ref={ref => {
            this.Scrollable = ref;
          }}
          closeOnDragDown
          height={appHelpInfoNew.length * 75}
          customStyles={{
            container: {
              borderTopLeftRadius: 36,
              borderTopRightRadius: 36,
            },
            draggableIcon: {
              width: 40,
              height: 4,
              borderRadius: 5,
              marginTop: 16,
              backgroundColor: colors.grayNeutral,
            },
          }}>
          <FlatList
            data={appHelpInfoNew}
            keyExtractor={(item, index) => item._id}
            ItemSeparatorComponent={this.renderSeparator}
            removeClippedSubviews={true}
            windowSize={10}
            renderItem={this.renderTrayItem}
          />
        </RBSheet>
      </View>
    );
  }
}

const mapStateToProps = (state) => ({
  appHelpInfo: state.auth.appHelpInfo,
	appHelpBadgeDate: state.auth.appHelpBadgeDate,
	appHelpLastChecked: state.auth.appHelpLastChecked,
});

export default connect(mapStateToProps)(withTranslation()(Dashboard));
