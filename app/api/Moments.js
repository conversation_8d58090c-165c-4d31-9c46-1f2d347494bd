import Meteor, { withTracker } from 'react-native-meteor';
import * as environmentSettings from '../shared/Settings';
import _ from 'underscore';
import People from '../api/People';
import Orgs from '../api/Orgs';
import moment from 'moment';
import MomentDefinitions from '../shared/MomentDefinitions';

const MomentMethods = {
	attachedMedia: function() {
		var outputMedia = [];
		if (this.mediaUrl && this.mediaUrl != "") {
			outputMedia.push({
				mediaUrl: this.mediaUrl,
				mediaToken: this.mediaToken,
				mediaFileType: this.mediaFileType,
				mediaPath: this.mediaPath
			});
		} 
		if (this.mediaFiles) outputMedia = outputMedia.concat(this.mediaFiles);

		var baseUrl = environmentSettings.APP_URL;
		if (baseUrl.slice(baseUrl.length-1) != "/") baseUrl+= "/";
		var momentOrgId = this.orgId;
		var momentCreatedBy = this.createdBy;		
		var momentId = this._id;

		_.each(outputMedia, function(m, i) {
			m.isApplication = (m.mediaFileType == "application");
			m.isVideo = (m.mediaFileType == "video");
			m.isPhoto = (!m.isVideo && m.mediaPath && m.mediaPath != "");
			
			m.mediaRedirectorPath = baseUrl + "m/" + momentId + "/" + i;
			if (m.mediaFileType=="video") {
				var mediaPath = momentOrgId + "/" + momentCreatedBy + "/" + m.mediaToken;
				var outputUrl = environmentSettings.PHOTO_BASE_URL + "output/video/";
				m.mediaVideoPath = outputUrl + mediaPath;
				m.pathForVideoFormat = function(videoFormat) {
					var mediaPath = momentOrgId + "/" + momentCreatedBy + "/" + m.mediaToken;
					var outputUrl = environmentSettings.PHOTO_BASE_URL + "output/video/";
					return outputUrl + mediaPath + "." + videoFormat;
				}
			} 

			var mediaPath = m.mediaPath;

			if (typeof mediaPath == 'undefined') mediaPath = momentOrgId + "/" + momentCreatedBy + "/" + m.mediaToken;
			m.getTimelinePhoto = environmentSettings.PHOTO_BASE_URL;
			if (m.mediaFileType == "image") {
				m.getTimelinePhoto += "resized/" + mediaPath + "-medium.jpg";
			} else if (m.mediaFileType == "video") {
				m.getTimelinePhoto += "output/thumbnails/" + mediaPath + "-00001.png";
			}
						
		});

		return outputMedia;
	},
	durationDescription: function() {
		var durationText = "";
		var startTime = new moment(this.time, "h:mm a");
		var endTime = new moment(this.endTime, "h:mm a");
		durationText += moment.duration(endTime - startTime).humanize() + " (" + this.time + " - " + this.endTime + ")";
		return durationText;
	},
	durationAmount: function() {
		var startTime = new moment(this.time, "h:mm a");
		var endTime = new moment(this.endTime, "h:mm a");
		
		if (startTime && endTime) {
			return moment.duration(endTime - startTime).asMinutes();
		}
		else 
			return 0;
	},
	subscribers: function() {
		var currentMoment = this;
		var outputSubscribers = [];
		Relationships.find({targetId: {$in: this.taggedPeople}, relationshipType: "family"}).forEach(function (r) {
			var p = People.findOne(r.personId);
			if (p && ( r.subscriptions && 
					   r.subscriptions[currentMoment.momentType] && 
					   (r.subscriptions[currentMoment.momentType]["email"] || 
					    r.subscriptions[currentMoment.momentType]["sms"] ||
					    r.subscriptions[currentMoment.momentType]["push"] )
					  ) 
				) 
			{
				p.subscriptions = r.subscriptions[currentMoment.momentType];
				p.activeRelationship = r;
				outputSubscribers.push(p);
			} else if (p && currentMoment.momentType == "alert") {
				p.subscriptions = {};
				p.subscriptions.email = true;
				p.subscriptions.sms = true;
				p.activeRelationship = r;
				outputSubscribers.push(p);
			}
		});	
		if (currentMoment.momentType == "alert") {
			People.find({_id: {$in: this.taggedPeople}, inActive:{$ne:true}, type: {$in: ["staff", "admin"]}}).forEach(function(p){
				p.subscriptions = {"email": true, "sms": true};
				p.activeRelationship = new Relationship({targetId: p._id});
				outputSubscribers.push(p);
			});
		}
		if (currentMoment.isDemo) {
			const recipientUser = Meteor.users.findOne({"_id": currentMoment.createdBy});
			let recipientPerson = recipientUser.fetchPerson();
			if (this.taggedPeople && this.taggedPeople.length > 0) {
				recipientPerson.subscriptions = {"email": true, "sms": true};
				recipientPerson.activeRelationship = new Relationship({targetId: this.taggedPeople[0]});
				outputSubscribers.push(recipientPerson);
			}
		}
		
		var md = MomentDefinitions.findOne({momentType: currentMoment.momentType, "notifications.required": true});
		if (md && md.notifications ) {
			_.each(md.notifications, function (notificationDef) {
				var subscriptions = {};
				_.each(notificationDef.channels, function(channel) { subscriptions[channel] = true; });

				_.each(notificationDef.recipientMatch, function(recipientRule) {
					recipientRule["orgId"] = currentMoment.orgId;
					recipientRule["inActive"] = {$ne:true};
					
					People.find(recipientRule).forEach(function(p) {

						p.subscriptions = subscriptions;
						p.activeRelationship = new Relationship({targetId: currentMoment.taggedPeople[0]});

						outputSubscribers.push(p);
					});
				});
			});
		}
		return outputSubscribers;
	},
	medicalAdministeredByName: function() {
		var adminByPerson = this.medicalAdministeredByPerson;
		if (adminByPerson && adminByPerson.fullName) {
			return adminByPerson.fullName
		} else if (adminByPerson && adminByPerson.firstName) {
			return `${adminByPerson.firstName} ${adminByPerson.lastName}`;
		} else {
			var p = People.findOne(this.medicalAdministeredBy);
			if (p) return p.firstName + ' ' + p.lastName;
		}
		return "";
	},
	foodAmountDescription: function() {

		if (this.foodType == "Bottle" || this.foodType == "Baby Food" || this.foodType == "Cereal") {
			const org = Orgs.findOne(this.orgId);
			var bottleDesc = "";
			if (this.foodBottleAmount) bottleDesc+="Bottle Amount: " + this.foodBottleAmount + " oz.\n";
			if (this.foodBottleAmountBreastmilkOffered) bottleDesc+="Breastmilk Offered: " + this.foodBottleAmountBreastmilkOffered + " oz.\n";
			if (this.foodBottleAmountBreastmilkConsumed) bottleDesc+="Breastmilk Consumed: " + this.foodBottleAmountBreastmilkConsumed + " oz.\n";
			if (this.foodBottleAmountFormulaOffered) bottleDesc+="Formula Offered: " + this.foodBottleAmountFormulaOffered + " oz.\n";
			if (this.foodBottleAmountFormulaConsumed) bottleDesc+="Formula Consumed: " + this.foodBottleAmountFormulaConsumed + " oz.\n";
			if (this.foodBottleAmountMilkOffered) bottleDesc+="Milk Offered: " + this.foodBottleAmountMilkOffered + " oz.\n";
			if (this.foodBottleAmountMilkConsumed) bottleDesc+="Milk Consumed: " + this.foodBottleAmountMilkConsumed + " oz.\n";
			if (this.foodBottleAmountBabyFoodConsumed) bottleDesc+="Food Consumed: " + this.foodBottleAmountBabyFoodConsumed + " " + org.foodUnits("babyFood") + "\n";
			if (this.foodBottleAmountCerealConsumed) bottleDesc+="Ceral Consumed: " + this.foodBottleAmountCerealConsumed + " " + org.foodUnits("cereal") + "\n";
			return bottleDesc;
		}
		else if (this.foodType == "Tube")
			return this.foodTubeAmount + " ml ";
		else 
			return this.foodAmount;
	},
	illnessSymptomDisplayString: function() {
		var output="";
		_.each(this.illnessSymptoms, function(i) {
			if (output != "") output+= ", ";
			output += i;
		});
		return output;
	},
	"sleepDuration": function() {
		var durationText = "";
		var startTime = new moment(this.time, "h:mm a");
		var endTime = new moment(this.endTime, "h:mm a");
		const duration = moment.duration(endTime - startTime)
		durationText += duration.hours() + ":" + duration.minutes().toString().padStart(2, '0') + " (" + this.time + " - " + this.endTime + ")";
		return durationText;
	},
	moodIcon: function() {
		switch (this.moodLevel || this.mood) {
			case "Happy":
				return "fa-smile-o";
			case "SoSo":
				return "fa-meh-o";
			case "Sad":
				return "fa-frown-o";
		}
	},
	likedByPerson: function(personId) {
		return this.reactions && this.reactions[personId];
	},
	peopleList: function() {
		var outstring = "";
		_.each(this.taggedPeople, function(d) {
			var person = People.findOne({orgId: Orgs.current()._id, _id: d});
			if (person) {
				if (outstring != "") outstring += "; ";
				outstring += person.lastName + ", " + person.firstName;
			}
		});
		return outstring;
	},
	momentTypePrettyTranslation: function() {
		const org = Orgs.current();
		if (!org) return;
  		var i18NValue = org.translate("momentTypes." + this.momentType + ".prettyName", this.momentTypePretty); 
		return i18NValue;
	},
	momentIconName: function() {
		let name = "icon-c-info";
		switch (this.momentType) {
			case "food":
				name = "icon-cutlery-77";
				break;
			case "potty":
				name = "icon-toilet-paper";
				break;
			case "sleep":
				name = "icon-bedroom";
				break;
			case "activity":
				name = "icon-sport-mode";
				break;
			case "medical":
				name = "icon-hospital-32";
				break;
			case "mood":
				name = "icon-quite-happy";
				break;
			case "supplies":
				name = "icon-design";
				break;
			case "learning":
				name = "icon-presentation";
				break;
			case "portfolio":
				name = "icon-folder-check";
				break;
			case "incident":
				name = "icon-c-warning";
				break;
			case "illness":
				name = "icon-temperature-2";
				break;
			case "ouch":
				name = "icon-patch-34";
				break;
			case "comment":
				name = "icon-comment";
				break;
			case "move":
				name = "icon-swap-horizontal";
				break;
			case "checkin":
				name = "icon-c-check";
				break;
			case "checkout":
				name = "icon-leave";
				break;
		}
		return name;
	},
	formattedDescription: function(outputType) {
		ouptutType = (typeof outputType !== 'undefined') ?  outputType : "html";
		var output = "";
		const currentUser = Meteor.user(), currentPerson = currentUser && People.findOne({_id: currentUser.personId});

		switch (this.momentType) {
			case "food":
				if (this.foodType || this.foodAmount) output+= "<b>Meal:</b> " ;
				if (this.foodAmount) {
					if (this.foodAmount == "Not Offered") {
						output += this.foodAmount
					} else {
						output += " Ate " + this.foodAmount;
					}
				}
				if (this.foodType) {
					if (this.foodAmount) {
						if (this.foodAmount == "Not Offered") {
							output += ` ${this.foodType}`
						} else {
							output += ` of ${this.foodType}`
						}
					} else {
						output += this.foodType
					}
				}
				if (output!="") output+= "<br/>";
				if (this.foodBabyFoodType) output += "<b>Type:</b> " + this.foodBabyFoodType + "<br/>";
				if (this.foodType == "Bottle" || this.foodType == "Baby Food" || this.foodType == "Cereal") output += this.foodAmountDescription().replace(/\n/g, "<br/>");
				if (this.foodType == "Tube" && this.foodTubeAmount) output += "<b>Amount:</b> " + this.foodTubeAmount + " ml<br/>";
				if (this.foodAmountPercent) output+="<b>Amount:</b> " + this.foodAmountPercent + "<br/>";
				if (this.foodItems) {
					this.foodItems.forEach( (fi) => {
						if (fi.amount) {
							output += `<b>${fi.name}:</b> ${fi.amount}<br/>`;
						} else {
							output += `${fi.name}<br/>`;
						}
					});
				}
				break;
			case "potty":
				var pottyTypeLabel = this.pottyType; //TAPi18n.__("momentTypes.potty.pottyTypeLabels." + this.pottyType, {defaultValue: this.pottyType});
				if (this.pottyType) output+= "<b>Type:</b> " + pottyTypeLabel + "<br/>";
				if (this.pottyTypeContinence) output+= this.pottyTypeContinence + "<br/>";
				if (this.pottyTraining) output += "<b>Training:</b> " + this.pottyTraining + "<br/>";
				if (this.pottyAppliedOintment) output += "Applied Ointment<br/>";
				break;
			case "sleep":
				if (this.endTime) 
					output+= "Slept for " + this.sleepDuration() + "<br/>";
				else
					output+= "Nap started<br/>";
				if (this.sleepDidNotSleep) output += "Did not sleep.<br/>";
				if (this.sleepSatQuietly) output += "Sat Quietly.<br/>";

				if (this.sleepChecks && currentPerson && _.contains(['admin','staff'], currentPerson.type)) {
					let sleepCheckDesc = "";
					_.chain(this.sleepChecks)
						.groupBy('personId')
						.map( (personSleepChecks, pid) => ({ personId: pid, lastSleepCheckEntry: _.max(personSleepChecks, (psc) => psc.createdAt )}))
						.each( (personWithLastSleepCheck) => {
							const person = People.findOne(personWithLastSleepCheck.personId),
								checkMoment = new moment(personWithLastSleepCheck.lastSleepCheckEntry.createdAt);
								const sleepPosition = (personWithLastSleepCheck.lastSleepCheckEntry.sleepPosition) ? `Sleep Position ${personWithLastSleepCheck.lastSleepCheckEntry.sleepPosition}` : "";
								const distressedSleep = (personWithLastSleepCheck.lastSleepCheckEntry.distressedSleep) ? `Signs of Distress: ${personWithLastSleepCheck.lastSleepCheckEntry.distressedSleep}<br/>` : "";
								if (person) sleepCheckDesc+= `${person.firstName} ${person.lastName} at ${checkMoment.format("hh:mm a")} ${sleepPosition}<br/>${distressedSleep}`;
						} );
					if (sleepCheckDesc != "") output+="<br/><b>Last sleep check(s):</b><br/>" + sleepCheckDesc;
				}
				break;
			case "activity":
				if (this.activityType) output+= "<b>Type:</b> " + this.activityType + "<br/>";
				if (this.activityEngagement) output+= "<b>Engagement:</b> " + this.activityEngagement + "<br/>";
				break;
			case "medical":
				if (this.medicalMedicineType) output+= "<b>Type:</b> " + this.medicalMedicineType +"<br/>";
				if (this.medicalMedicationName) output+= "<b>Medication:</b> " + this.medicalMedicationName + "<br/>";
				if (this.medicalMedicineAmount) output+= "<b>Amount:</b> " + this.medicalMedicineAmount + "<br/>";
				if (this.medicalDoctorName) output+= "<b>Doctor:</b> " + this.medicalDoctorName + "<br/>";
				if (this.medicalMedication) output+= this.medicalMedication.name + " " + this.medicalMedication.dosage + "<br/>";
				output += "<b>Administered By:</b> " + this.medicalAdministeredByName() + "<br/>";
				break;
			case "mood":
				if (this.moodLevel)
					output+= "<i class=\"fa " + this.moodIcon() + " fa-2x\"></i><br/>";
				break;
			case "prospect":
				if (this.prospectType) output+= "<b>Type:</b> " + this.prospectType + "<br/>";
				break;
			case "supplies":
				output += "<b>Supply Needed:</b> " + this.supplyType + "<br/>";
				break;
			case "learning":
				if (this.learningType) output += "<b>Type:</b> " + this.learningType.replace("`", " > ") + "<br/>";
				if (this.learningCurriculum?.headline) output += "<b>Activity:</b> " + this.learningCurriculum?.headline.replace("`", " > ") + "<br/>";
				const assessmentLevels = Orgs.current() && Orgs.current().learningAssessmentLevels();
				if (this.learningAssessmentLevel != null) {
					
					output += "<b>Assessment:</b> " + ((assessmentLevels && assessmentLevels.length >= this.learningAssessmentLevel) ? assessmentLevels[this.learningAssessmentLevel].label : this.learningAssessmentLevel) + "<br/>";
				}
				break;
			case "portfolio":
				if (this.portfolioCurriculum)
					output += "<b>Activity:</b> " + this.portfolioCurriculum.headline + "<br/>";
				const allStandards = Curriculums.getStandards(),
					availableAssessmentLevels = Orgs.current() && Orgs.current().getAvailableAssessmentLevels();
				_.each( this.portfolioAssessments, (assessment) => {
					const matchedStandard = _.find(allStandards, (s) => s.standardId == assessment.standardId),
						matchedAssessmentLevel = _.find(availableAssessmentLevels, (l) => l.value == assessment.value) ;
					if (matchedStandard && matchedAssessmentLevel) {
						output += `<b>${matchedStandard.category ? matchedStandard.category : matchedStandard.source}:</b> ${matchedStandard.benchmark}<br/>`;
						output += `${matchedAssessmentLevel.label} <br/>`;
					}
				});
				break;
			case "incident":
				if (this.incidentNature) output += "<b>Nature:</b> " + this.incidentNature + "<br/>";
				if (this.incidentLocation) output += "<b>Location:</b> " + this.incidentLocation + "<br/>";
				if (this.incidentActionTaken) output += "<b>Action Taken:</b> " + this.incidentActionTaken + "<br/>";
				break;
			case "illness":
				output+="<b>Symptoms:</b> " + this.illnessSymptomDisplayString() + "<br/>";
				output+= `<br/>IT IS REQUIRED TO KEEP OR TAKE A CHILD HOME WHEN THE CHILD:<br/><br/>Is suffering from one or more of the above symptoms.<br/><br/>Is not well enough to take part in the daily activities.<br/><br/>A child will be symptom free, without the aid of symptom reducing medications, for a full 24 hours prior to returning to school. We do reserve the right to ask for a note from your family doctor, depending on the illness/disease.<br/>`;
				break;
			case "ouch":
				if (this.ouchDescription) output += "<b>Injury/Accident Description:</b> " + this.ouchDescription + "<br/>";
				if (this.ouchCare) output += "<b>Care Provided:</b> " + this.ouchCare + "<br/>";
				if (this.ouchCalledParent || this.ouchContactedParent) {
					output += "<b>Parent Contacted:</b> " + this.ouchContactedParent ;
					if (this.ouchCalledParentTime) output+= " at " + this.ouchCalledParentTime;
					output+="<br/>";
				}
				if (this.ouchCalledDoctor || this.ouchContactedDoctor) {
					output += "<b>Doctor Contacted:</b> " + this.ouchContactedDoctor;
					if (this.ouchCalledDoctorTime) output += " at " + this.ouchCalledDoctorTime;
					output += "<br/>";
				}
				if (this.ouchNurseNotified) output+= "Nurse Notified<br/>";
				if (this.ouchProfessionalMedication) output+= "Professional Medication Provided<br/>";
				break;
			default:
				if (this.isDynamic) {
					const currentOrg = Orgs.findOne(this.orgId),
						momentDefinitions = currentOrg && new MomentDefinitions(this.momentType, {org: currentOrg}),
						momentDefinition = momentDefinitions && momentDefinitions.getDefinition();
					if (momentDefinition) {
						
						for (const momentField of momentDefinition.fields) {
							var fieldValue = this.dynamicFieldValues[momentField.dataId];
							var displayFieldValue = "";
							if (momentField.fieldType == "select") {
								var searchValues =  momentField.multi ? fieldValue : [fieldValue];
								_.each(searchValues, function (sv) {
									const definitionDisplayFieldValue = _.find(momentField.fieldValues, function (dfv) { return dfv.fieldValue==sv; });
									if (definitionDisplayFieldValue)
										displayFieldValue += ((displayFieldValue == "") ? "" : ", ") + definitionDisplayFieldValue.fieldValueLabel;
								});
							} else {
								displayFieldValue = fieldValue;
							}
							if (fieldValue) output += `<b>${momentField.label}:</b> ${displayFieldValue}<br/>`;
						}
						
					}
				}
		}
		return output.replace(new RegExp("<br\/>$"), "");
	},
	taggedPeopleDescription(isFamily = false,selectedPersonId = null) {
		let peopleDescription = "",
			firstPerson = null,
			peopleNames = [],
			setFirstPerson = false;

		let isFind = -1
		try{
			isFind = this.taggedPeople.indexOf(selectedPersonId);
			if(selectedPersonId && isFind != -1){
				const query = { _id: selectedPersonId};
				if (isFamily) query.type = { $in: ["family", "person"] };
				const person = People.findOne(query);
				if (person && !setFirstPerson) {
					firstPerson = person;
					setFirstPerson = true;
				}
			}
		}catch(e){
			console.log("ERROR=>"+JSON.stringify(e))
		}finally{
			_.each( this.taggedPeople, (pId) => {
				if(isFind != -1 && selectedPersonId && selectedPersonId == pId){
					return
				}
				const query = { _id: pId};
				if (isFamily) query.type = { $in: ["family", "person"] };
				const person = People.findOne(query);
				if (person && !setFirstPerson) {
					firstPerson = person;
					setFirstPerson = true;
				} else if (person && setFirstPerson) {
					peopleNames.push( person.firstName + " " + person.lastName);
				}
			});
			if (peopleNames.length == 1) {
				peopleDescription = peopleNames[0];
			} else if (peopleNames.length == 2) {
				peopleDescription = peopleNames[0] + " and " + peopleNames[1];
			} else if (peopleNames.length > 2) {
				peopleDescription = peopleNames[0] + " with " + (peopleNames.length - 1) + " others";
			}
		}
		return {
			peopleDescription,
			firstPerson,
		};
	}
};

Moments = new Meteor.Collection('moments', {
	transform: function(doc) {
		return _.extend(doc, MomentMethods);
	}
});

export default Moments;
