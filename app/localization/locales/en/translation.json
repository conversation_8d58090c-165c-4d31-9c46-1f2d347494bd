{"navigation": {"dashboard": "Dashboard", "people": "People", "activity": "Activity", "gallery": "Gallery", "moments": "Moments", "settings": "Settings"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "back": "Back", "next": "Next", "done": "Done", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No"}, "authentication": {"signIn": "Sign In", "signOut": "Sign Out", "username": "Username", "password": "Password", "pinCode": "PIN Code", "supplementalPinCode": "Supplemental PIN Code", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "enterCode": "Enter Code", "emailAddress": "Email Address", "baseUrl": "Base URL", "passwordHelp": "Password help", "verifyPin": "<PERSON><PERSON><PERSON>", "ssoLogin": "SSO Login", "saveAndConnect": "Save and Connect", "getConnectionStatus": "Get Connection Status", "useUsernamePassword": "Use Username and Password", "usePinCode": "Already logged in? Use your PIN code", "enterUsernameFirst": "Please enter your username first.", "forgotPasswordError": "Error with <PERSON><PERSON> Password", "checkEmailVerification": "Please check your email for a verification code", "mustEnterUsername": "You must enter a username", "invalidCredentials": "Invalid username or password", "networkError": "Network error. Please try again.", "loginError": "<PERSON><PERSON> failed. Please try again."}, "people": {"checkIn": "Check In", "checkOut": "Check Out", "move": "Move", "moveToGroup": "Move to...", "selectGroup": "Choose group", "absentToday": "Absent Today", "scheduledToday": "Scheduled Today", "notScheduledToday": "Not Scheduled Today", "completedBy": "Completed By", "chooseAction": "Choose Action", "sleepCheck": "Sleep Check", "nameToFace": "Name to Face", "search": "Search", "allGroups": "All Groups"}, "forms": {"title": "Title", "notes": "Notes", "reason": "Reason", "amount": "Amount", "type": "Type", "date": "Date", "time": "Time", "description": "Description"}, "status": {"checkedIn": "Checked In", "checkedOut": "Checked Out", "absent": "Absent", "present": "Present", "scheduled": "Scheduled", "notScheduled": "Not Scheduled"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "retry": "Retry", "refresh": "Refresh"}, "settings": {"selectLanguage": "Select Language", "language": "Language", "changeLanguage": "Change Language", "english": "English", "spanish": "Spanish"}, "dashboard": {"headerTitle": "Dashboard", "upNext": "Up-Next", "arrivingSoon": "{{count}} Arriving Soon", "leavingSoon": "{{count}} Leaving Soon", "here": "{{count}} Here", "outlook": "Outlook", "announcements": "Announcements", "greeting": "Hey {{firstName}}!", "checkedIntoGroup": "You are checked into {{groupName}}", "checkedIn": "You are checked in", "notCheckedIn": "You are not checked in", "staffImpact": {"title": "Your Impact Today", "defaultMessage": "Be great today! Check here for an update on your progress."}, "lightbridgeResources": {"title": "Important Resources", "description": "Here are links to important resources:", "tortal": "<PERSON><PERSON>", "tortalDescription": "Lightbridge Training", "intranet": "Intranet", "intranetDescription": "Lightbridge News and Documentation"}, "lightbridgePeek": {"title": "Peek of the Week", "header": "Peek of the Week", "noDataMessage": "No peeks available for today. Stay tuned!"}, "survey": {"title": "How are we doing?", "question": "We made some changes to the home page! Are you enjoying those changes?", "happyFollowUp": "Great! Which changes do you like:", "sadFollowUp": "What needs improvement?:", "upNextSection": "Up-Next section", "outlookSection": "Outlook section", "expressDriveUpSection": "Express Drive-Up", "announcementsSection": "Announcements", "anythingElse": "Anything Else?", "submitFeedback": "Submit <PERSON>"}, "sleep": {"title": "Who's <PERSON><PERSON><PERSON>"}, "timeCard": {"title": "Time Card Approval", "reviewTitle": "Review Time Cards", "period": "Period", "timeReported": "Time Reported", "changePeriod": "Change Period", "approveSelected": "Approve Selected", "pendingApproval": "You've got time to approve", "review": "Review"}, "dataValidation": {"title": "Is your information up-to-date?", "verifyButton": "Verify Information"}, "tray": {"chatSupport": "Chat with the school", "whatsNew": "What's New", "learningPath": "LearningPath"}, "campaign": {"resourcesTitle": "Resources and Information", "surveyTitle": "How are we doing?", "viewButton": "View", "submitButton": "Submit <PERSON>", "cancelButton": "Cancel", "ratingResponse": "You gave us a '{{rating}}'...", "thankYouMessage": "Thanks so much for your feedback.", "followUpQuestions": {"improve": "How can we get it right next time?", "delight": "How can we delight you next time?", "delighted": "How did we delight you?"}, "alerts": {"errorTitle": "Error", "errorMessage": "We experienced an error saving your response, please try again", "successTitle": "Thanks!", "successMessage": "Your feedback submitted successfully"}}}}